﻿using GenerateAbpAngularModule.DomainModel;
using Microsoft.Extensions.Configuration;
using System.Diagnostics;
using System.Net.Mime;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Text.Json;
using System.Text.RegularExpressions;
using Humanizer;

namespace GenerateAbpAngularModule
{
    internal class Program
    {
        static string? EntityName;
        static string? FileName;
        static string? EntityNameLowercase;
        static string? EntityNamePlural;
        static string? Form;
        static string? Table;
        static string? FormEdit;
        static string? dir;
        static string? Enums;
        static AppSettings? _appSettings;

        static async Task Main(string[] args)
        {
            Console.WriteLine("Start Create Angular Page");
            var builder = new ConfigurationBuilder()
                .SetBasePath(AppContext.BaseDirectory)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true);

            var configuration = builder.Build();
            _appSettings = configuration.Get<AppSettings>();

            if (string.IsNullOrEmpty(_appSettings?.EntityName))
            {
                Console.WriteLine($"File:EntityName Not Configure");
                return;
            }
            if (string.IsNullOrEmpty(_appSettings?.AngularPath))
            {
                Console.WriteLine($"Configuration:AngularPath Not Configure");
                return;
            }

            if (!Directory.Exists(_appSettings?.AngularPath))
            {
                Console.WriteLine($"Configuration:AngularPath Not Found");
                return;
            }

            EntityName = _appSettings?.EntityName;
            dir = _appSettings?.AngularPath;

            var jsonPath = $"./Entity/{EntityName}.json";

            if (!File.Exists(jsonPath))
            {
                Console.WriteLine($"File:{EntityName}.json Not Found");
                return;
            }
            string jsonString = await File.ReadAllTextAsync(jsonPath);
            var entityModels = JsonSerializer.Deserialize<List<Entity>>(jsonString);

            EntityNameLowercase = ToFirstLower(EntityName);//首字母转小写
            EntityNamePlural = EntityNameLowercase.Pluralize();//复数

            var output = ExecuteCommand("abp generate-proxy -t ng", dir);
            Console.WriteLine(output);
            if (output.StartsWith("Error: [API Not Available]"))
            {
                Console.WriteLine("Service Not Start");
                return;
            }
            FileName = GenerateModule(EntityName);

            var folderName = "./Generate";
            Directory.CreateDirectory(folderName);

            DeleteAllFiles(folderName);

            CopyAllFiles("./Sample", folderName);

            ReplaceInFolderFileNames(folderName, "{{FileName}}", FileName);

            if (entityModels != null && entityModels.Count > 0)
            {
                foreach (var entityModel in entityModels)
                {
                    GenerateHtml(entityModel.Name, entityModel.Type.ToLower(), entityModel.Required, entityModel.EnumName);
                }
            }

            var htmlFilName = $"{FileName}.component.html";
            var tsFilName = $"{FileName}.component.ts";

            ReplaceInFileContent($"{folderName}/{htmlFilName}", "{{EntityName}}", EntityName);
            ReplaceInFileContent($"{folderName}/{htmlFilName}", "{{EntityNameLowercase}}", EntityNameLowercase);
            ReplaceInFileContent($"{folderName}/{htmlFilName}", "{{EntityNamePlural}}", EntityNamePlural);
            ReplaceInFileContent($"{folderName}/{htmlFilName}", "{{Form}}", Form);
            ReplaceInFileContent($"{folderName}/{htmlFilName}", "{{Table}}", Table);
            ReplaceInFileContent($"{folderName}/{htmlFilName}", "{{FormEdit}}", FormEdit);

            ReplaceInFileContent($"{folderName}/{tsFilName}", "{{EntityName}}", EntityName);
            ReplaceInFileContent($"{folderName}/{tsFilName}", "{{EntityNameLowercase}}", EntityNameLowercase);
            ReplaceInFileContent($"{folderName}/{tsFilName}", "{{EntityNamePlural}}", EntityNamePlural);
            ReplaceInFileContent($"{folderName}/{tsFilName}", "{{Form}}", Form);
            ReplaceInFileContent($"{folderName}/{tsFilName}", "{{Table}}", Table);
            ReplaceInFileContent($"{folderName}/{tsFilName}", "{{FormEdit}}", FormEdit);
            ReplaceInFileContent($"{folderName}/{tsFilName}", "{{FileName}}", FileName);
            ReplaceInFileContent($"{folderName}/{tsFilName}", "{{Enums}}", Enums);

            CopyAllFiles(folderName, $"{dir}//src//app//{FileName}");
            Console.WriteLine("Succeed");
            Console.ReadKey();
        }
        static void GenerateHtml(string Name, string type, bool required, string EnumName)
        {
            Console.WriteLine($"{Name}----{type}----{required}");
            var _name = ToFirstLower(Name);
            var requireStr = required ? ", Validators.required" : "";
            var requireHtml = required ? "<span> * </span>" : "";
            switch (type)
            {
                case "guid":
                case "string":
                    Form += $"{_name}: [this.selected{EntityName}.{_name} || ''{requireStr}]," + Environment.NewLine;
                    Table += $@"<ngx-datatable-column [name]=""'::{EntityName}{Name}' | abpLocalization"" prop=""{_name}""></ngx-datatable-column>" + Environment.NewLine;
                    FormEdit += @$"<div class=""mt-2"">
      <label for=""{EntityNameLowercase}-{_name}"">{{{{ '::{EntityName}{Name}'| abpLocalization }}}}</label>{requireHtml}
      <input type=""text"" id=""{EntityNameLowercase}-{_name}"" class=""form-control"" formControlName=""{_name}""  />
    </div>
" + Environment.NewLine;
                    break;
                case "decimal":
                case "int":
                case "long":
                case "float":
                case "double":
                    Form += $"{_name}: [this.selected{EntityName}.{_name} || null {requireStr}]," + Environment.NewLine;
                    Table += $@"   <ngx-datatable-column [name]=""'::{EntityName}{Name}' | abpLocalization"" prop=""{_name}"">
        <ng-template let-row=""row"" ngx-datatable-cell-template>
          {{{{ row.{_name} }}}}
        </ng-template>
      </ngx-datatable-column>" + Environment.NewLine;
                    FormEdit += @$"<div class=""mt-2"">
      <label for=""{EntityNameLowercase}-{_name}"">{{{{ '::{EntityName}{Name}'| abpLocalization }}}}</label>{requireHtml}
      <input type=""number"" id=""{EntityNameLowercase}-{_name}"" class=""form-control"" formControlName=""{_name}""  />
    </div>
" + Environment.NewLine;
                    break;
                case "datetime":
                case "date":
                    Form += $"{_name}: [this.selected{EntityName}.{_name} ? new Date(this.selected{EntityName}.{_name}) : null{requireStr}]," + Environment.NewLine;
                    Table += $@"   <ngx-datatable-column [name]=""'::{EntityName}{Name}' | abpLocalization"" prop=""{_name}"">
        <ng-template let-row=""row"" ngx-datatable-cell-template>
          {{{{ row.{_name} | date }}}}
        </ng-template>
      </ngx-datatable-column>" + Environment.NewLine;
                    FormEdit += @$"<div class=""mt-2"">
      <label for=""{EntityNameLowercase}-{_name}"">{{{{ '::{EntityName}{Name}'| abpLocalization }}}}</label>{requireHtml}
       <input #{_name}Picker=""ngbDatepicker"" id=""{EntityNameLowercase}-{_name}"" class=""form-control"" name=""{_name}Picker"" formControlName=""{_name}""
                ngbDatepicker (click)=""{_name}Picker.toggle()"" />
    </div>
" + Environment.NewLine;
                    break;
                case "bool":
                    Form += $"{_name}: [this.selected{EntityName}.{_name} {requireStr}]," + Environment.NewLine;
                    Table += $@"<ngx-datatable-column [name]=""'::{EntityName}{Name}' | abpLocalization"" prop=""{_name}"">
        <ng-template let-row=""row"" ngx-datatable-cell-template>
          <input class=""form-check-input ng-valid ng-dirty ng-touched"" type=""checkbox"" [checked]=""row.{_name}"" disabled>
        </ng-template>
      </ngx-datatable-column>" + Environment.NewLine;
                    FormEdit += @$"<div class=""mt-2"">
    <input type=""checkbox"" id=""{EntityNameLowercase}-{_name}"" class=""form-check-input me-1"" formControlName=""{_name}"">
              <label class=""form-check-label"" for=""{EntityNameLowercase}-{_name}"">{{{{ '::{EntityName}{Name}'| abpLocalization }}}}</label>{requireHtml}
    </div>
" + Environment.NewLine;
                    break;
                case "enum":
                    if (string.IsNullOrEmpty(EnumName))
                    {
                        Console.WriteLine("Enum type EnumName must be set.");
                        Environment.Exit(0);
                    }
                    var _enumName = $"{ToFirstLower(EnumName)}";
                    Form += $"{_name}: [null{requireStr}]," + Environment.NewLine;
                    Table += $@"<ngx-datatable-column [name]=""'::{EntityName}{Name}' | abpLocalization"" prop=""{_name}"">
                                <ng-template let-row=""row"" ngx-datatable-cell-template>
                                  {{{{ '::Enum:{EnumName}.' + row.{_name} | abpLocalization }}}}
                                </ng-template>
                              </ngx-datatable-column>" + Environment.NewLine;
                    FormEdit += @$"<div class=""mt-2"">
                                       <label for=""{EntityNameLowercase}-{_name}"">{{{{ '::{EntityName}{Name}'| abpLocalization }}}}</label>{requireHtml}
                                        <select class=""form-control"" id=""{EntityNameLowercase}-{_name}"" formControlName=""{_name}"">
                                            <option [ngValue]=""null"">Select a {Name}</option>
                                            <option [ngValue]=""{_name}.value"" *ngFor=""let {_name} of {_enumName}""> {{{{ '::Enum:{EntityName}{Name}.' + {_name}.value | abpLocalization }}}}  </option>
                                        </select>
                                   </div>" + Environment.NewLine;
                    Enums += $" {_enumName} = {_enumName}Options;" + Environment.NewLine;

                    break;
            }
        }
        static string ToFirstLower(string str)
        {
            return char.ToLower(str[0]) + str.Substring(1);
        }
        public static string GenerateModule(string className)
        {
            var cmd = $"yarn ng generate module {className} --module app --routing --route {className}s";
            var output = ExecuteCommand(cmd, dir);
            Console.WriteLine(output);
            string folderName = ExtractFolderName(output);
            return folderName;
        }
        static string ExecuteCommand(string command, string workingDirectory)
        {
            string shell, shellArgs;
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                shell = "cmd.exe";
                shellArgs = $"/C {command}";
            }
            else
            {
                shell = "/bin/bash";
                shellArgs = $"-c \"{command}\"";
            }
            ProcessStartInfo processStartInfo = new ProcessStartInfo
            {
                FileName = shell,
                Arguments = shellArgs,
                WorkingDirectory = workingDirectory,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                UseShellExecute = false,
                CreateNoWindow = true
            };

            using (Process process = new Process())
            {
                process.StartInfo = processStartInfo;
                process.Start();
                string output = process.StandardOutput.ReadToEnd();
                string error = process.StandardError.ReadToEnd();
                process.WaitForExit();
                if (string.IsNullOrEmpty(error))
                {
                    return output;
                }
                else
                {
                    return $"Error: {error}";
                }
            }
        }
        static string ExtractFolderName(string input)
        {
            // 定义正则表达式模式
            string pattern = @"\bsrc/app/([^/]+)";

            // 使用正则表达式匹配模式
            Match match = Regex.Match(input, pattern);

            // 检查是否找到匹配项
            if (match.Success)
            {
                // 返回匹配的文件夹名字
                return match.Groups[1].Value;
            }
            else
            {
                // 没有找到匹配项
                return "Folder name not found";
            }
        }
        static void CopyAllFiles(string sourceDir, string targetDir)
        {

            /// 获取源文件夹中的所有文件
            string[] files = Directory.GetFiles(sourceDir);

            // 复制每个文件到目标文件夹
            foreach (string file in files)
            {
                // 获取文件名
                string fileName = Path.GetFileName(file);
                // 拼接目标文件路径
                string destFile = Path.Combine(targetDir, fileName);
                // 复制文件
                File.Copy(file, destFile, true); // 设置 overwrite 参数为 true 可以覆盖已存在的文件
            }
        }
        static void DeleteAllFiles(string folderPath)
        {

            // 检查文件夹是否存在
            if (!Directory.Exists(folderPath))
            {
                Console.WriteLine("Folder does not exist.");
                return;
            }

            // 获取文件夹中的所有文件
            string[] files = Directory.GetFiles(folderPath);

            // 删除每个文件
            foreach (string file in files)
            {
                File.Delete(file);
            }

        }
        static void ReplaceInFolderFileNames(string folderPath, string oldValue, string newValue)
        {
            // 检查文件夹是否存在
            if (!Directory.Exists(folderPath))
            {
                Console.WriteLine("Folder does not exist.");
                return;
            }

            // 获取文件夹中的所有文件
            string[] files = Directory.GetFiles(folderPath);

            // 遍历每个文件并进行替换
            foreach (string filePath in files)
            {
                // 获取文件名
                string fileName = Path.GetFileName(filePath);

                // 进行替换
                string newFileName = fileName.Replace(oldValue, newValue);

                // 构建新的文件路径
                string newFilePath = Path.Combine(folderPath, newFileName);

                // 重命名文件
                File.Move(filePath, newFilePath);
            }
        }
        static void ReplaceInFileContent(string filePath, string oldValue, string newValue)
        {
            // 检查文件是否存在
            if (!File.Exists(filePath))
            {
                Console.WriteLine("File does not exist.");
                return;
            }

            // 读取文件内容
            string content = File.ReadAllText(filePath);

            // 替换内容
            content = content.Replace(oldValue, newValue);

            // 写回文件
            File.WriteAllText(filePath, content);
        }

    }
}
