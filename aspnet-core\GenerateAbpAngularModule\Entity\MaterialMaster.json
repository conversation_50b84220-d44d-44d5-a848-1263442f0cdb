[{"Name": "ItemNo", "Type": "String", "Required": true, "EnumName": ""}, {"Name": "ItemName", "Type": "String", "Required": true, "EnumName": ""}, {"Name": "ItemType", "Type": "String", "Required": false, "EnumName": ""}, {"Name": "ItemClass", "Type": "Enum", "Required": true, "EnumName": "ItemClass"}, {"Name": "U<PERSON>", "Type": "String", "Required": true, "EnumName": ""}, {"Name": "ShelfLifeDays", "Type": "int", "Required": false, "EnumName": ""}, {"Name": "BatchManage", "Type": "Bool", "Required": false, "EnumName": ""}, {"Name": "StockOutMode", "Type": "Enum", "Required": false, "EnumName": "StockOutMode"}, {"Name": "UnitPrice", "Type": "decimal", "Required": false, "EnumName": ""}, {"Name": "<PERSON><PERSON>", "Type": "Bool", "Required": false, "EnumName": ""}, {"Name": "InvalidDate", "Type": "Datetime", "Required": false, "EnumName": ""}, {"Name": "Version", "Type": "String", "Required": false, "EnumName": ""}, {"Name": "OutSourcing", "Type": "Bool", "Required": false, "EnumName": ""}, {"Name": "SafetyStock", "Type": "decimal", "Required": false, "EnumName": ""}]