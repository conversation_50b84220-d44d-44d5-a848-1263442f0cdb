<abp-card>
  <div class="card-header">
    <div class="row">
      <div class="col col-md-6">
        <h5 class="card-title">{{ '::SalesOrderItem' | abpLocalization }}</h5>
      </div>
      <div class="text-end col col-md-6">
        <div class="text-lg-end">
            <button *abpPermission="'Assistant.SalesOrderItem.Create'" id="create" class="btn btn-primary me-2" type="button"
                    (click)="createSalesOrderItem()">
                <i class="fa fa-plus me-1"></i>
                <span>{{ "::CreateSalesOrderItem" | abpLocalization }}</span>
            </button>
            <button *abpPermission="'Assistant.SalesOrderItem.Create'" id="export" class="btn btn-secondary me-2" type="button"
                    (click)="exportSalesOrderItem()">
                <i class="fa fa-file-excel-o me-1"></i>
                <span>{{ "::ExportExcel" | abpLocalization }}</span>
            </button>
        </div>
      </div>
    </div>
  </div>

  <abp-card-body>
    <div id="data-tables-table-filter" class="data-tables-filter mb-3">
      <div class="input-group">
        <input type="search" class="form-control" [placeholder]="'AbpUi::PagerSearch' | abpLocalization"
          [(ngModel)]="list.filter" />
      </div>
    </div>

    <ngx-datatable [rows]="salesOrderItems.items" [count]="salesOrderItems.totalCount" [list]="list" default>
      <ngx-datatable-column [name]="'::Actions' | abpLocalization" [maxWidth]="150" [sortable]="false">
        <ng-template let-row="row" ngx-datatable-cell-template>
          <div ngbDropdown container="body" class="d-inline-block">
            <button class="btn btn-primary btn-sm dropdown-toggle" data-toggle="dropdown" aria-haspopup="true"
              ngbDropdownToggle>
              <i class="fa fa-cog me-1"></i>{{ '::Actions' | abpLocalization }}
            </button>
            <div ngbDropdownMenu>
              <button ngbDropdownItem (click)="editSalesOrderItem(row.id)" *abpPermission="'Assistant.SalesOrderItem.Update'">
                {{ '::Edit' | abpLocalization }}
              </button>
              <button ngbDropdownItem (click)="delete(row.id)" *abpPermission="'Assistant.SalesOrderItem.Delete'">
                {{ '::Delete' | abpLocalization }}
              </button>
            </div>
          </div>
        </ng-template>
      </ngx-datatable-column>

     <ngx-datatable-column [name]="'::SalesOrderItemSalesOrderId' | abpLocalization" prop="salesOrderId"></ngx-datatable-column>
   <ngx-datatable-column [name]="'::SalesOrderItemSalesItem' | abpLocalization" prop="salesItem">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.salesItem }}
        </ng-template>
      </ngx-datatable-column>
<ngx-datatable-column [name]="'::SalesOrderItemProjectNo' | abpLocalization" prop="projectNo"></ngx-datatable-column>
<ngx-datatable-column [name]="'::SalesOrderItemItemId' | abpLocalization" prop="itemId"></ngx-datatable-column>
<ngx-datatable-column [name]="'::SalesOrderItemVersion' | abpLocalization" prop="version"></ngx-datatable-column>
   <ngx-datatable-column [name]="'::SalesOrderItemQuantity' | abpLocalization" prop="quantity">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.quantity }}
        </ng-template>
      </ngx-datatable-column>
   <ngx-datatable-column [name]="'::SalesOrderItemUnitPrice' | abpLocalization" prop="unitPrice">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.unitPrice }}
        </ng-template>
      </ngx-datatable-column>
<ngx-datatable-column [name]="'::SalesOrderItemTransfered' | abpLocalization" prop="transfered">
        <ng-template let-row="row" ngx-datatable-cell-template>
          <input class="form-check-input ng-valid ng-dirty ng-touched" type="checkbox" [checked]="row.transfered" disabled>
        </ng-template>
      </ngx-datatable-column>
   <ngx-datatable-column [name]="'::SalesOrderItemTransferedQuantity' | abpLocalization" prop="transferedQuantity">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.transferedQuantity }}
        </ng-template>
      </ngx-datatable-column>
   <ngx-datatable-column [name]="'::SalesOrderItemDeliveredQuantity' | abpLocalization" prop="deliveredQuantity">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.deliveredQuantity }}
        </ng-template>
      </ngx-datatable-column>
<ngx-datatable-column [name]="'::SalesOrderItemStatus' | abpLocalization" prop="status">
                                <ng-template let-row="row" ngx-datatable-cell-template>
                                  {{ '::Enum:SalesOrderStatus.' + row.status | abpLocalization }}
                                </ng-template>
                              </ngx-datatable-column>
   <ngx-datatable-column [name]="'::SalesOrderItemRequireDate' | abpLocalization" prop="requireDate">
        <ng-template let-row="row" ngx-datatable-cell-template>
          {{ row.requireDate | date }}
        </ng-template>
      </ngx-datatable-column>
<ngx-datatable-column [name]="'::SalesOrderItemCustomerItemNo' | abpLocalization" prop="customerItemNo"></ngx-datatable-column>
<ngx-datatable-column [name]="'::SalesOrderItemComment' | abpLocalization" prop="comment"></ngx-datatable-column>


     
    </ngx-datatable>
  </abp-card-body>
</abp-card>




<abp-modal [(visible)]="isModalOpen" [options]="options">
  <ng-template #abpHeader>
    <h3>{{ (selectedSalesOrderItem.id ? '::EditSalesOrderItem' : '::CreateSalesOrderItem' ) | abpLocalization }}</h3>
  </ng-template>

  <ng-template #abpBody>
    <form [formGroup]="form" (ngSubmit)="save()">
      <div class="mt-2">
      <label for="salesOrderItem-salesOrderId">{{ '::SalesOrderItemSalesOrderId'| abpLocalization }}</label><span> * </span>
      <input type="text" id="salesOrderItem-salesOrderId" class="form-control" formControlName="salesOrderId"  />
    </div>

<div class="mt-2">
      <label for="salesOrderItem-salesItem">{{ '::SalesOrderItemSalesItem'| abpLocalization }}</label><span> * </span>
      <input type="number" id="salesOrderItem-salesItem" class="form-control" formControlName="salesItem"  />
    </div>

<div class="mt-2">
      <label for="salesOrderItem-projectNo">{{ '::SalesOrderItemProjectNo'| abpLocalization }}</label><span> * </span>
      <input type="text" id="salesOrderItem-projectNo" class="form-control" formControlName="projectNo"  />
    </div>

<div class="mt-2">
      <label for="salesOrderItem-itemId">{{ '::SalesOrderItemItemId'| abpLocalization }}</label>
      <input type="text" id="salesOrderItem-itemId" class="form-control" formControlName="itemId"  />
    </div>

<div class="mt-2">
      <label for="salesOrderItem-version">{{ '::SalesOrderItemVersion'| abpLocalization }}</label>
      <input type="text" id="salesOrderItem-version" class="form-control" formControlName="version"  />
    </div>

<div class="mt-2">
      <label for="salesOrderItem-quantity">{{ '::SalesOrderItemQuantity'| abpLocalization }}</label><span> * </span>
      <input type="number" id="salesOrderItem-quantity" class="form-control" formControlName="quantity"  />
    </div>

<div class="mt-2">
      <label for="salesOrderItem-unitPrice">{{ '::SalesOrderItemUnitPrice'| abpLocalization }}</label><span> * </span>
      <input type="number" id="salesOrderItem-unitPrice" class="form-control" formControlName="unitPrice"  />
    </div>

<div class="mt-2">
    <input type="checkbox" id="salesOrderItem-transfered" class="form-check-input" formControlName="transfered">
              <label class="form-check-label" for="salesOrderItem-transfered">{{ '::SalesOrderItemTransfered'| abpLocalization }}</label><span> * </span>
    </div>

<div class="mt-2">
      <label for="salesOrderItem-transferedQuantity">{{ '::SalesOrderItemTransferedQuantity'| abpLocalization }}</label><span> * </span>
      <input type="number" id="salesOrderItem-transferedQuantity" class="form-control" formControlName="transferedQuantity"  />
    </div>

<div class="mt-2">
      <label for="salesOrderItem-deliveredQuantity">{{ '::SalesOrderItemDeliveredQuantity'| abpLocalization }}</label><span> * </span>
      <input type="number" id="salesOrderItem-deliveredQuantity" class="form-control" formControlName="deliveredQuantity"  />
    </div>

<div class="mt-2">
                                       <label for="salesOrderItem-status">{{ '::SalesOrderItemStatus'| abpLocalization }}</label>
                                        <select class="form-control" id="salesOrderItem-status" formControlName="status">
                                            <option [ngValue]="null">Select a Status</option>
                                            <option [ngValue]="status.value" *ngFor="let status of salesOrderStatus"> {{ '::Enum:SalesOrderItemStatus.' + status.value | abpLocalization }}  </option>
                                        </select>
                                   </div>
<div class="mt-2">
      <label for="salesOrderItem-requireDate">{{ '::SalesOrderItemRequireDate'| abpLocalization }}</label><span> * </span>
       <input #requireDatePicker="ngbDatepicker" id="salesOrderItem-requireDate" class="form-control" name="requireDatePicker" formControlName="requireDate"
                ngbDatepicker (click)="requireDatePicker.toggle()" />
    </div>

<div class="mt-2">
      <label for="salesOrderItem-customerItemNo">{{ '::SalesOrderItemCustomerItemNo'| abpLocalization }}</label>
      <input type="text" id="salesOrderItem-customerItemNo" class="form-control" formControlName="customerItemNo"  />
    </div>

<div class="mt-2">
      <label for="salesOrderItem-comment">{{ '::SalesOrderItemComment'| abpLocalization }}</label>
      <input type="text" id="salesOrderItem-comment" class="form-control" formControlName="comment"  />
    </div>


    </form>
  </ng-template>

  <ng-template #abpFooter>
    <button type="button" class="btn btn-secondary" abpClose>
      {{ '::Close' | abpLocalization }}
    </button>

    <button class="btn btn-primary" [disabled]="form.invalid" (click)="save()">
      <i class="fa fa-check mr-1"></i>
      {{ '::Save' | abpLocalization }}
    </button>
  </ng-template>
</abp-modal>
