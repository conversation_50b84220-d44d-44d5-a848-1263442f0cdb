//if (type != null)
//{
//    PropertyInfo[] properties = type.GetProperties();
//    foreach (PropertyInfo property in properties)
//    {
//        var required = false;
//        if (property.CustomAttributes.Any(x => x.AttributeType.Name == "RequiredAttribute"))
//            required = true;
//        var propertytype = property.PropertyType.Name;
//        if (propertytype == "Nullable`1")
//            propertytype = property.PropertyType.GenericTypeArguments.FirstOrDefault()?.Name;
//        if (property.PropertyType.IsEnum)
//        {
//            propertytype = "Enum";
//            var enumModel = new EnumModel()
//            {
//                PropertyName = property.PropertyType.Name,
//                Values = new()
//            };
//            Type enumType = property.PropertyType;
//            var enumNames = Enum.GetNames(enumType);
//            foreach (var name in enumNames)
//            {
//                var value = Convert.ToInt32(Enum.Parse(enumType, name));
//                enumModel.Values.Add(new()
//                {
//                    Name = name,
//                    Value = value
//                });
//            }
//            EnumModels.Add(enumModel);
//        }
//        GenerateHtml(property.Name, propertytype, required, property.PropertyType.Name);
//    }
//}