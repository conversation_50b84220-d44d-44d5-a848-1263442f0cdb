[{"Name": "SalesOrderId", "Type": "String", "Required": true, "EnumName": ""}, {"Name": "SalesItem", "Type": "int", "Required": true, "EnumName": ""}, {"Name": "ProjectNo", "Type": "string", "Required": true, "EnumName": ""}, {"Name": "ItemId", "Type": "String", "Required": false, "EnumName": ""}, {"Name": "Version", "Type": "String", "Required": false, "EnumName": ""}, {"Name": "Quantity", "Type": "decimal", "Required": true, "EnumName": ""}, {"Name": "UnitPrice", "Type": "decimal", "Required": true, "EnumName": ""}, {"Name": "Transfered", "Type": "bool", "Required": true, "EnumName": ""}, {"Name": "TransferedQuantity", "Type": "decimal", "Required": true, "EnumName": ""}, {"Name": "DeliveredQuantity", "Type": "decimal", "Required": true, "EnumName": ""}, {"Name": "Status", "Type": "Enum", "Required": false, "EnumName": "SalesOrderStatus"}, {"Name": "RequireDate", "Type": "datetime", "Required": true, "EnumName": ""}, {"Name": "CustomerItemNo", "Type": "String", "Required": false, "EnumName": ""}, {"Name": "Comment", "Type": "String", "Required": false, "EnumName": ""}]