import { ListService, PagedResultDto } from '@abp/ng.core';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbDateNativeAdapter, NgbDateAdapter } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmationService, Confirmation } from '@abp/ng.theme.shared';
import { IdentityUserDto } from '@abp/ng.identity/proxy';
import { firstValueFrom } from 'rxjs';
@Component({
    selector: 'app-{{FileName}}',
    templateUrl: './{{FileName}}.component.html',
    styleUrl: './{{FileName}}.component.scss',
    providers: [
        ListService,
        { provide: NgbDateAdapter, useClass: NgbDateNativeAdapter } 
    ],
})
export class {{EntityName}}Component implements OnInit {

    {{EntityNamePlural}} = { items: [], totalCount: 0 } as PagedResultDto<{{EntityName}}Dto>;
    
    {{Enums}}
    isModalOpen = false;
    form: FormGroup; 
    selected{{EntityName}} = {} as {{EntityName}}Dto;
    options = {
        size: 'lg',
        // fullscreen: true,
        scrollable: true
    };

    constructor(public readonly list: ListService,
        private {{EntityNameLowercase}}Service: {{EntityName}}Service,
        private fb: FormBuilder,
        private confirmation: ConfirmationService
    ) { }

    ngOnInit(): void {
        const bookStreamCreator = (query) => this.{{EntityNameLowercase}}Service.getList(query);
        this.list.hookToQuery(bookStreamCreator).subscribe((response) => {
            this.{{EntityNamePlural}} = response;
        });
    }




export{{EntityName}}() {
    // this.fileService.downloadTempFile(result);
}

    create{{EntityName}}() {
        this.selected{{EntityName}} = {  } as {{EntityName}}Dto;
        this.buildForm();
        this.isModalOpen = true;
    }


    delete(id: string) {
        this.confirmation.warn('::AreYouSureToDelete', '::AreYouSure').subscribe((status) => {
            if (status === Confirmation.Status.confirm) {
                this.{{EntityNameLowercase}}Service.delete(id).subscribe(() => this.list.get());
            }
        });
    }

    edit{{EntityName}}(id: string) {
        this.{{EntityNameLowercase}}Service.get(id).subscribe(async (project) => {
            this.selected{{EntityName}} = project;
            this.buildForm();
            this.isModalOpen = true;
        });
    }



    buildForm() {
        this.form = this.fb.group({
            {{Form}}
        });
    }


    save() {
        if (this.form.invalid) {
            return;
        }
        const request = this.selected{{EntityName}}.id
            ? this.{{EntityNameLowercase}}Service.update(this.selected{{EntityName}}.id, this.form.value)
            : this.{{EntityNameLowercase}}Service.create(this.form.value);
            
        request.subscribe(() => {
            this.isModalOpen = false; 
            this.form.reset(); 
            this.list.get(); 
        });
    }
}
