<abp-card>
  <div class="card-header">
    <div class="row">
      <div class="col col-md-6">
        <h5 class="card-title">{{ '::{{EntityName}}' | abpLocalization }}</h5>
      </div>
      <div class="text-end col col-md-6">
        <div class="text-lg-end">
            <button *abpPermission="'Assistant.{{EntityName}}.Create'" id="create" class="btn btn-primary me-2" type="button"
                    (click)="create{{EntityName}}()">
                <i class="fa fa-plus me-1"></i>
                <span>{{ "::Create{{EntityName}}" | abpLocalization }}</span>
            </button>
            <button *abpPermission="'Assistant.{{EntityName}}.Create'" id="export" class="btn btn-secondary me-2" type="button"
                    (click)="export{{EntityName}}()">
                <i class="fa fa-file-excel-o me-1"></i>
                <span>{{ "::ExportExcel" | abpLocalization }}</span>
            </button>
        </div>
      </div>
    </div>
  </div>

  <abp-card-body>
    <div id="data-tables-table-filter" class="data-tables-filter mb-3">
      <div class="input-group">
        <input type="search" class="form-control" [placeholder]="'AbpUi::PagerSearch' | abpLocalization"
          [(ngModel)]="list.filter" />
      </div>
    </div>

    <ngx-datatable [rows]="{{EntityNamePlural}}.items" [count]="{{EntityNamePlural}}.totalCount" [list]="list" default>
      <ngx-datatable-column [name]="'::Actions' | abpLocalization" [maxWidth]="150" [sortable]="false">
        <ng-template let-row="row" ngx-datatable-cell-template>
          <div ngbDropdown container="body" class="d-inline-block">
            <button class="btn btn-primary btn-sm dropdown-toggle" data-toggle="dropdown" aria-haspopup="true"
              ngbDropdownToggle>
              <i class="fa fa-cog me-1"></i>{{ '::Actions' | abpLocalization }}
            </button>
            <div ngbDropdownMenu>
              <button ngbDropdownItem (click)="edit{{EntityName}}(row.id)" *abpPermission="'Assistant.{{EntityName}}.Update'">
                {{ '::Edit' | abpLocalization }}
              </button>
              <button ngbDropdownItem (click)="delete(row.id)" *abpPermission="'Assistant.{{EntityName}}.Delete'">
                {{ '::Delete' | abpLocalization }}
              </button>
            </div>
          </div>
        </ng-template>
      </ngx-datatable-column>

     {{Table}}

     
    </ngx-datatable>
  </abp-card-body>
</abp-card>




<abp-modal [(visible)]="isModalOpen" [options]="options">
  <ng-template #abpHeader>
    <h3>{{ (selected{{EntityName}}.id ? '::Edit{{EntityName}}' : '::Create{{EntityName}}' ) | abpLocalization }}</h3>
  </ng-template>

  <ng-template #abpBody>
    <form [formGroup]="form" (ngSubmit)="save()">
      {{FormEdit}}
    </form>
  </ng-template>

  <ng-template #abpFooter>
    <button type="button" class="btn btn-secondary" abpClose>
      {{ '::Close' | abpLocalization }}
    </button>

    <button class="btn btn-primary" [disabled]="form.invalid" (click)="save()">
      <i class="fa fa-check mr-1"></i>
      {{ '::Save' | abpLocalization }}
    </button>
  </ng-template>
</abp-modal>
