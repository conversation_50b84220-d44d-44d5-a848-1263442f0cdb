import { ListService, PagedResultDto } from '@abp/ng.core';
import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbDateNativeAdapter, NgbDateAdapter } from '@ng-bootstrap/ng-bootstrap';
import { ConfirmationService, Confirmation } from '@abp/ng.theme.shared';
import { IdentityUserDto } from '@abp/ng.identity/proxy';
import { firstValueFrom } from 'rxjs';
@Component({
    selector: 'app-sales-order-item',
    templateUrl: './sales-order-item.component.html',
    styleUrl: './sales-order-item.component.scss',
    providers: [
        ListService,
        { provide: NgbDateAdapter, useClass: NgbDateNativeAdapter } 
    ],
})
export class SalesOrderItemComponent implements OnInit {

    salesOrderItems = { items: [], totalCount: 0 } as PagedResultDto<SalesOrderItemDto>;
    
     salesOrderStatus = salesOrderStatusOptions;

    isModalOpen = false;
    form: FormGroup; 
    selectedSalesOrderItem = {} as SalesOrderItemDto;
    options = {
        size: 'lg',
        // fullscreen: true,
        scrollable: true
    };

    constructor(public readonly list: ListService,
        private salesOrderItemService: SalesOrderItemService,
        private fb: FormBuilder,
        private confirmation: ConfirmationService
    ) { }

    ngOnInit(): void {
        const bookStreamCreator = (query) => this.salesOrderItemService.getList(query);
        this.list.hookToQuery(bookStreamCreator).subscribe((response) => {
            this.salesOrderItems = response;
        });
    }




exportSalesOrderItem() {
    // this.fileService.downloadTempFile(result);
}

    createSalesOrderItem() {
        this.selectedSalesOrderItem = {  } as SalesOrderItemDto;
        this.buildForm();
        this.isModalOpen = true;
    }


    delete(id: string) {
        this.confirmation.warn('::AreYouSureToDelete', '::AreYouSure').subscribe((status) => {
            if (status === Confirmation.Status.confirm) {
                this.salesOrderItemService.delete(id).subscribe(() => this.list.get());
            }
        });
    }

    editSalesOrderItem(id: string) {
        this.salesOrderItemService.get(id).subscribe(async (project) => {
            this.selectedSalesOrderItem = project;
            this.buildForm();
            this.isModalOpen = true;
        });
    }



    buildForm() {
        this.form = this.fb.group({
            salesOrderId: [this.selectedSalesOrderItem.salesOrderId || '', Validators.required],
salesItem: [this.selectedSalesOrderItem.salesItem || null , Validators.required],
projectNo: [this.selectedSalesOrderItem.projectNo || '', Validators.required],
itemId: [this.selectedSalesOrderItem.itemId || ''],
version: [this.selectedSalesOrderItem.version || ''],
quantity: [this.selectedSalesOrderItem.quantity || null , Validators.required],
unitPrice: [this.selectedSalesOrderItem.unitPrice || null , Validators.required],
transfered: [this.selectedSalesOrderItem.transfered , Validators.required],
transferedQuantity: [this.selectedSalesOrderItem.transferedQuantity || null , Validators.required],
deliveredQuantity: [this.selectedSalesOrderItem.deliveredQuantity || null , Validators.required],
status: [null],
requireDate: [this.selectedSalesOrderItem.requireDate ? new Date(this.selectedSalesOrderItem.requireDate) : null, Validators.required],
customerItemNo: [this.selectedSalesOrderItem.customerItemNo || ''],
comment: [this.selectedSalesOrderItem.comment || ''],

        });
    }


    save() {
        // 检查表单是否无效，如果是，则不执行后续操作
        if (this.form.invalid) {
            return;
        }
        // 根据所选项目是否有ID，选择调用更新或创建服务方法
        const request = this.selectedSalesOrderItem.id
            ? this.salesOrderItemService.update(this.selectedSalesOrderItem.id, this.form.value)
            : this.salesOrderItemService.create(this.form.value);

        // 订阅服务请求的结果，成功时执行一系列操作
        request.subscribe(() => {
            this.isModalOpen = false; // 关闭模态对话框
            this.form.reset(); // 重置表单
            this.list.get(); // 获取更新后的项目列表
        });
    }
}
